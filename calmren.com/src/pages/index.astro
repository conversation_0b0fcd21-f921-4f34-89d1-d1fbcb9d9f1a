---
import Layout from '../layouts/Layout.astro';
---

<Layout title="calmren - Calm ops, smart systems">
  <!-- Hero Section -->
  <section class="container-padding py-20 lg:py-32">
    <div class="max-w-4xl mx-auto text-center">
      <h1 class="text-4xl lg:text-6xl font-bold text-near-black mb-6">
        Calm ops, smart systems.
      </h1>
      <p class="text-xl lg:text-2xl text-volcanic-grey mb-8 max-w-3xl mx-auto">
        Making sense of tech chaos with a bit of kiwi ingenuity – because good operations should enhance, be simple and smart.
      </p>
      <a href="/contact" class="inline-block bg-pounamu text-white px-8 py-4 rounded-lg text-lg font-medium hover:bg-sea-blue transition-colors">
        Let's Kōrero
      </a>
    </div>
  </section>

  <!-- Value Proposition -->
  <section class="container-padding py-16 bg-white">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h2 class="text-3xl lg:text-4xl font-semibold text-near-black mb-4">
          Distilling 18 Months of Deep Dive into AI.
        </h2>
        <p class="text-lg text-volcanic-grey max-w-3xl mx-auto">
          AI thrives not in complexity, but in practical application where context reigns supreme. AI is a powerful tool, but it is not a magic wand; its successful implementation requires a clear strategy and a deep understanding of its capabilities and limitations
      </div>
    </div>
  </section>

  <!-- Services Overview -->
  <section class="container-padding py-20">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl lg:text-4xl font-semibold text-near-black text-center mb-16">
        What I Do
      </h2>
      <div class="grid md:grid-cols-3 gap-8">
        <div class="text-center p-8">
          <h3 class="text-xl font-semibold text-near-black mb-4">Strategic Advisory</h3>
          <p class="text-volcanic-grey">
            Helping leadership make sense of the tech landscape and turn buzzwords into bottom-line wins.
          </p>
        </div>
        <div class="text-center p-8">
          <h3 class="text-xl font-semibold text-near-black mb-4">Fractional COO</h3>
          <p class="text-volcanic-grey">
            Hands-on operational leadership to untangle knots and build foundations that amplify impact.
          </p>
        </div>
        <div class="text-center p-8">
          <h3 class="text-xl font-semibold text-near-black mb-4">Automation Audits</h3>
          <p class="text-volcanic-grey">
            Practical audits to find efficiency gains and design automation that saves hours, not headaches.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Principles -->
  <section class="container-padding py-20 bg-white">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl lg:text-4xl font-semibold text-near-black text-center mb-16">
        My Approach
      </h2>
      <div class="grid md:grid-cols-3 gap-12">
        <div class="text-center">
          <h3 class="text-2xl font-semibold text-pounamu mb-4">Smart</h3>
          <p class="text-volcanic-grey">
            Measurable impact over theatre. Every solution is designed for real-world results, not just impressive demos.
          </p>
        </div>
        <div class="text-center">
          <h3 class="text-2xl font-semibold text-pounamu mb-4">Safe</h3>
          <p class="text-volcanic-grey">
            Privacy-aware by design. Security-first delivery with Privacy Act-aware setups and compliant cloud solutions.
          </p>
        </div>
        <div class="text-center">
          <h3 class="text-2xl font-semibold text-pounamu mb-4">Simple</h3>
          <p class="text-volcanic-grey">
            Humans first, fewer moving parts. Technology should serve people, not create more complexity.
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- Operational Symptoms Carousel -->
  <section class="container-padding py-20">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-2xl lg:text-3xl font-semibold text-near-black mb-4">
        It's rarely just one thing.
      </h2>
      <p class="text-lg text-volcanic-grey mb-12">
        Common operational symptoms we see:
      </p>

      <div class="relative">
        <!-- Carousel Container -->
        <div id="symptoms-carousel" class="overflow-hidden cursor-pointer" onclick="window.location.href='/services'">
          <div id="carousel-track" class="flex transition-transform duration-500 ease-in-out">
            <!-- Slide 1 -->
            <div class="w-full flex-shrink-0 px-4">
              <div class="bg-white p-8 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <p class="text-lg lg:text-xl text-volcanic-grey">
                  An M365 setup that's powerful but unconfigured and insecure.
                </p>
              </div>
            </div>

            <!-- Slide 2 -->
            <div class="w-full flex-shrink-0 px-4">
              <div class="bg-white p-8 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <p class="text-lg lg:text-xl text-volcanic-grey">
                  Dozens of subscriptions with overlapping features and wasted budget.
                </p>
              </div>
            </div>

            <!-- Slide 3 -->
            <div class="w-full flex-shrink-0 px-4">
              <div class="bg-white p-8 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <p class="text-lg lg:text-xl text-volcanic-grey">
                  Teams using AI and other tools without policies, creating security risks.
                </p>
              </div>
            </div>

            <!-- Slide 4 -->
            <div class="w-full flex-shrink-0 px-4">
              <div class="bg-white p-8 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <p class="text-lg lg:text-xl text-volcanic-grey">
                  Manual, repetitive processes that could be easily automated.
                </p>
              </div>
            </div>

            <!-- Slide 5 -->
            <div class="w-full flex-shrink-0 px-4">
              <div class="bg-white p-8 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                <p class="text-lg lg:text-xl text-volcanic-grey">
                  No clear data from your social media or marketing tools to tell you what's working.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Navigation Dots -->
        <div class="flex justify-center mt-8 space-x-2">
          <button class="carousel-dot w-3 h-3 rounded-full bg-pounamu transition-opacity" data-slide="0"></button>
          <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors" data-slide="1"></button>
          <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors" data-slide="2"></button>
          <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors" data-slide="3"></button>
          <button class="carousel-dot w-3 h-3 rounded-full bg-gray-300 hover:bg-gray-400 transition-colors" data-slide="4"></button>
        </div>

        <!-- Click hint -->
        <p class="text-sm text-pounamu mt-4 opacity-75">
          Click to learn how we can help →
        </p>
      </div>
    </div>
  </section>

  <script>
    // Carousel functionality
    let currentSlide = 0;
    const totalSlides = 5;
    const track = document.getElementById('carousel-track');
    const dots = document.querySelectorAll('.carousel-dot');

    function updateCarousel() {
      const translateX = -currentSlide * 100;
      track.style.transform = `translateX(${translateX}%)`;

      // Update dots
      dots.forEach((dot, index) => {
        if (index === currentSlide) {
          dot.classList.remove('bg-gray-300');
          dot.classList.add('bg-pounamu');
        } else {
          dot.classList.remove('bg-pounamu');
          dot.classList.add('bg-gray-300');
        }
      });
    }

    function nextSlide() {
      currentSlide = (currentSlide + 1) % totalSlides;
      updateCarousel();
    }

    // Auto-advance carousel
    setInterval(nextSlide, 4000);

    // Dot navigation
    dots.forEach((dot, index) => {
      dot.addEventListener('click', (e) => {
        e.stopPropagation(); // Prevent navigation to services page
        currentSlide = index;
        updateCarousel();
      });
    });
  </script>

  <!-- Final CTA -->
  <section class="container-padding py-20 bg-pounamu text-white">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl lg:text-4xl font-semibold mb-6">
        Bring clarity to your ops in 90 minutes
      </h2>
      <p class="text-xl mb-8 opacity-90">
        Whether you need a pragmatic strategist or someone to turn chaos into calm, let's kōrero.
      </p>
      <a href="/contact" class="inline-block bg-white text-pounamu px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-colors">
        Book a 30-min intro
      </a>
    </div>
  </section>
</Layout>
