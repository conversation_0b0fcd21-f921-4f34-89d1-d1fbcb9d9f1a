---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Lab - calmren" description="Projects, experiments, and technical work that showcase practical solutions to real-world problems.">
  <!-- Hero Section -->
  <section class="container-padding py-20 lg:py-32">
    <div class="max-w-4xl mx-auto text-center">
      <h1 class="text-4xl lg:text-6xl font-bold text-near-black mb-6">
        Lab
      </h1>
      <p class="text-xl lg:text-2xl text-volcanic-grey mb-8 max-w-3xl mx-auto">
        Projects, experiments, and technical work that showcase practical solutions to real-world problems.
      </p>
    </div>
  </section>

  <!-- Projects Grid -->
  <section class="container-padding py-20">
    <div class="max-w-6xl mx-auto">
      <h2 class="text-3xl font-semibold text-near-black text-center mb-16">
        Current Projects
      </h2>
      
      <!-- Project Cards Grid -->
      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        
        <!-- This Website -->
        <div class="bg-white rounded-lg p-6 border border-gray-200 hover:border-pounamu transition-colors">
          <h3 class="text-xl font-semibold text-near-black mb-3">calmren.com</h3>
          <p class="text-volcanic-grey mb-4 text-sm">
            This website itself – built with Astro, Tailwind CSS, and deployed on Cloudflare Pages. 
            A showcase of the "calm, intelligent" design philosophy in action.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Astro</span>
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Tailwind</span>
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Cloudflare</span>
          </div>
          <a href="https://github.com/calmren/calmren.com" class="text-pounamu hover:text-sea-blue transition-colors text-sm font-medium">
            View on GitHub →
          </a>
        </div>

        <!-- Placeholder Project 1 -->
        <div class="bg-white rounded-lg p-6 border border-gray-200">
          <h3 class="text-xl font-semibold text-near-black mb-3">NFP Operations Dashboard</h3>
          <p class="text-volcanic-grey mb-4 text-sm">
            A real-time operations dashboard for a national NFP, streamlining donor engagement 
            and media strategy with privacy-first data handling.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Power BI</span>
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Power Automate</span>
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">SharePoint</span>
          </div>
          <p class="text-volcanic-grey text-sm">
            Case study coming soon
          </p>
        </div>

        <!-- Placeholder Project 2 -->
        <div class="bg-white rounded-lg p-6 border border-gray-200">
          <h3 class="text-xl font-semibold text-near-black mb-3">AI Workflow Automation</h3>
          <p class="text-volcanic-grey mb-4 text-sm">
            Secure AI-powered workflow automation that processes documents while keeping 
            sensitive data within approved environments.
          </p>
          <div class="flex flex-wrap gap-2 mb-4">
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Azure AI</span>
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Logic Apps</span>
            <span class="bg-gray-100 text-volcanic-grey px-2 py-1 rounded text-xs">Compliance</span>
          </div>
          <p class="text-volcanic-grey text-sm">
            Technical write-up in progress
          </p>
        </div>

      </div>
    </div>
  </section>

  <!-- Technical Background -->
  <section class="container-padding py-20 bg-white">
    <div class="max-w-4xl mx-auto">
      <h2 class="text-3xl font-semibold text-near-black text-center mb-16">
        Technical Background
      </h2>
      <div class="grid md:grid-cols-2 gap-12">
        <div>
          <h3 class="text-xl font-semibold text-near-black mb-4">Experience</h3>
          <ul class="space-y-3 text-volcanic-grey">
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              Ex-Microsoft engineer with hands-on tech chops
            </li>
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              Built teams across NZ & AU government projects
            </li>
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              18 months deep in AI and automation
            </li>
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              Security-first delivery with Privacy Act compliance
            </li>
          </ul>
        </div>
        <div>
          <h3 class="text-xl font-semibold text-near-black mb-4">Current Focus</h3>
          <ul class="space-y-3 text-volcanic-grey">
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              Cloud-native solutions (Azure, AWS, GCP)
            </li>
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              AI/ML integration with proper governance
            </li>
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              Process automation and workflow optimisation
            </li>
            <li class="flex items-start">
              <span class="text-pounamu mr-3">•</span>
              Modern web development and API design
            </li>
          </ul>
        </div>
      </div>
    </div>
  </section>

  <!-- GitHub Integration Placeholder -->
  <section class="container-padding py-20">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl font-semibold text-near-black mb-8">
        Open Source Contributions
      </h2>
      <div class="bg-white rounded-lg p-12 border border-gray-200">
        <p class="text-lg text-volcanic-grey mb-8">
          GitHub integration coming soon. This section will automatically showcase selected repositories, 
          contributions, and technical experiments.
        </p>
        <p class="text-volcanic-grey mb-8">
          In the meantime, you can check out my work directly on GitHub.
        </p>
        <a href="https://github.com/calmren" class="inline-block bg-pounamu text-white px-6 py-3 rounded-lg hover:bg-sea-blue transition-colors">
          View GitHub Profile
        </a>
      </div>
    </div>
  </section>

  <!-- CTA -->
  <section class="container-padding py-20 bg-pounamu text-white">
    <div class="max-w-4xl mx-auto text-center">
      <h2 class="text-3xl lg:text-4xl font-semibold mb-6">
        Interested in the technical details?
      </h2>
      <p class="text-xl mb-8 opacity-90">
        Let's discuss how these approaches might work for your specific challenges.
      </p>
      <a href="/contact" class="inline-block bg-white text-pounamu px-8 py-4 rounded-lg text-lg font-medium hover:bg-gray-100 transition-colors">
        Let's Kōrero
      </a>
    </div>
  </section>
</Layout>
