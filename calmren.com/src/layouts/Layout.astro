---
import '../styles/global.css';

export interface Props {
  title: string;
  description?: string;
  ogImage?: string;
}

const { title, description = "Calm ops, smart systems. Feeling like your team's juggling too many systems that don't talk to each other? Wondering if you're missing out on AI opportunities while staying secure? You're not alone.", ogImage = "/og-image.jpg" } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="description" content={description} />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    
    <!-- SEO Meta Tags -->
    <title>{title}</title>
    <meta property="og:title" content={title} />
    <meta property="og:description" content={description} />
    <meta property="og:image" content={ogImage} />
    <meta property="og:type" content="website" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content={title} />
    <meta name="twitter:description" content={description} />
    <meta name="twitter:image" content={ogImage} />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </head>
  <body class="bg-off-white text-near-black">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-pounamu text-white px-4 py-2 rounded">
      Skip to main content
    </a>
    
    <!-- Navigation -->
    <nav class="container-padding py-6">
      <div class="max-w-6xl mx-auto flex justify-between items-center">
        <a href="/" class="text-xl font-semibold text-near-black hover:text-pounamu transition-colors">
          calmren
        </a>
        <div class="hidden md:flex space-x-8">
          <a href="/services" class="text-near-black hover:text-pounamu transition-colors">Services</a>
          <a href="/ai-help" class="text-near-black hover:text-pounamu transition-colors">AI Help</a>
          <a href="/lab" class="text-near-black hover:text-pounamu transition-colors">Lab</a>
          <a href="/korero" class="text-near-black hover:text-pounamu transition-colors">Kōrero</a>
          <a href="/contact" class="bg-pounamu text-white px-4 py-2 rounded hover:bg-sea-blue transition-colors">
            Let's Kōrero
          </a>
        </div>
        <!-- Mobile menu button -->
        <button class="md:hidden text-near-black" aria-label="Open menu">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </nav>

    <!-- Main Content -->
    <main id="main-content">
      <slot />
    </main>

    <!-- Footer -->
    <footer class="container-padding py-12 mt-24 border-t border-gray-200">
      <div class="max-w-6xl mx-auto text-center">
        <p class="text-volcanic-grey text-sm">
          © 2025 calmren. Built with care in Aotearoa New Zealand.
        </p>
        <div class="mt-4 flex justify-center space-x-6">
          <a href="mailto:<EMAIL>" class="text-volcanic-grey hover:text-pounamu transition-colors">
            Email
          </a>
          <a href="https://linkedin.com/in/calmren" class="text-volcanic-grey hover:text-pounamu transition-colors">
            LinkedIn
          </a>
        </div>
      </div>
    </footer>
  </body>
</html>
