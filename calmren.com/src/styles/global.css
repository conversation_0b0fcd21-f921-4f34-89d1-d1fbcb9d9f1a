/* Import premium fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";

/* Tailwind CSS v4 Configuration */
@theme {
  --color-near-black: #1D1D1F;
  --color-off-white: #F5F5F7;
  --color-light-grey: #F5F5F7;
  --color-pounamu: #2E4D4D;
  --color-sea-blue: #1A3A5A;
  --color-volcanic-grey: #4A4A4A;

  --font-family-sans: 'Inter', 'Manrope', 'Satoshi', system-ui, sans-serif;

  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;
  --font-size-6xl: 3.75rem;

  --line-height-xs: 1.6;
  --line-height-sm: 1.6;
  --line-height-base: 1.7;
  --line-height-lg: 1.7;
  --line-height-xl: 1.6;
  --line-height-2xl: 1.6;
  --line-height-3xl: 1.5;
  --line-height-4xl: 1.4;
  --line-height-5xl: 1.3;
  --line-height-6xl: 1.2;

  --spacing-18: 4.5rem;
  --spacing-22: 5.5rem;
  --spacing-26: 6.5rem;
  --spacing-30: 7.5rem;
  --spacing-34: 8.5rem;
  --spacing-38: 9.5rem;
}

/* CSS Custom Properties for backwards compatibility */
:root {
  --color-near-black: #1D1D1F;
  --color-off-white: #F5F5F7;
  --color-pounamu: #2E4D4D;
  --color-sea-blue: #1A3A5A;
  --color-volcanic-grey: #4A4A4A;

  --font-family-sans: 'Inter', system-ui, sans-serif;
  --line-height-relaxed: 1.7;
  --line-height-comfortable: 1.6;
}

/* Base styles following Apple aesthetic */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family-sans);
  font-display: swap;
  scroll-behavior: smooth;
}

body {
  background-color: var(--color-off-white);
  color: var(--color-near-black);
  line-height: var(--line-height-relaxed);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: var(--line-height-comfortable);
  letter-spacing: -0.025em;
}

/* Smooth transitions for interactive elements */
a, button {
  transition: all 0.2s ease-out;
}

/* Focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--color-pounamu);
  outline-offset: 2px;
}

/* Utility classes for consistent spacing */
.container-padding {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

@media (min-width: 768px) {
  .container-padding {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-padding {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}