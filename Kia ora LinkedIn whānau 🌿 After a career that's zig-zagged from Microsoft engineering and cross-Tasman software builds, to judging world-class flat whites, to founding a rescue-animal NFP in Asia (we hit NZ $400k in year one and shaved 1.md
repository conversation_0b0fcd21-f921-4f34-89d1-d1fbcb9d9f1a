<PERSON><PERSON> ora <PERSON>edIn whānau 🌿 After a career that's zig-zagged from Microsoft engineering and cross-Tasman software builds, to judging world-class flat whites, to founding a rescue-animal NFP in Asia (we hit NZ $400k in year one and shaved 18 months off the tax-exempt waitlist)… I'm officially pivoting again. 

The last 18 months deep in AI taught me this: strategy and ops only sing when the systems are smart, safe, and simple. So I'm putting my "kiwi" mindset to work \- helping orgs turn messy data, patch-work processes, and under-used talent into calm, intelligent operations.

🔸 What I bring:   
• Hands-on tech chops – ex-MS engineer, built teams across NZ & AU gov projects  
• Commercial nous from bootstrap hospitality ventures  
• Proven NFP playbook – strategy, comms, community, compliance on a shoestring  
• Fresh AI and automation – turning manual workflows into reliable, human-friendly systems  
• **Security-first delivery** – Privacy Act-aware setups, compliant cloud, Power Automate done safely, and no accidental data in public AI tools

🔸 What I'm doing right now: Working with a national NFP to untangle their operational knots – streamlining donor engagement, overhauling their media strategy, and building tech foundations that'll amplify their impact (rather than create more admin headaches). The kind of work where you can see the lightbulb moments happening in real-time.

🔸 What I'm looking for: Advisory gigs, fractional COO work, and collaborations where tech meets people for real-world impact. Whether that's helping small businesses figure out if AI is actually worth their time (spoiler: sometimes it's not), conducting practical business audits that cut through the noise, or designing automation that saves hours not headaches. Especially keen on work across Aotearoa and APAC where we can build something meaningful together.

If you need a pragmatic strategist who can turn buzzwords into bottom-line wins (and shout the next long black), let's kōrero. DM me or email 📮. Ngā mihi nu

Of course. That's a fantastic origin story and a crystal-clear value proposition. Let's translate that "calm, intelligent operations" vibe into a website spec for calmren.com.  
The goal is to create a digital presence that feels like an exhale – professional, clean, and deeply competent, mirroring the outcome you provide for your clients. We'll lean heavily into the Apple-inspired aesthetic: minimalism, impeccable typography, and meaningful visuals.

Here is a detailed spec to flesh out the vision for your website.

---

### **Project: calmren.com Website**

#### 1\. Core Philosophy & Guiding Principles:

* **Clarity Over Clutter:** Every element must have a purpose. If it doesn't clarify your message or guide the user, it goes.  
* **Calm Intelligence:** The user experience should feel effortless and reassuring. Smooth animations, intuitive navigation, and a calm colour palette.  
* **Pragmatism Embodied:** The site should be fast, accessible, and easy to maintain. It's a reflection of the efficient systems you build. The tech should serve the message, not the other way around.

#### 2\. Design, UI & UX (The "Apple" Aesthetic):

* **Typography:** This will do the heavy lifting.  
  * **Font:** A clean, variable sans-serif font like **Inter**, **Manrope**, or **Satoshi**. These are modern, highly readable, and have a "tech-premium" feel. Use a variety of weights (e.g., Light for paragraphs, Medium for subheadings, Bold for headlines) to create hierarchy without adding clutter.  
  * **Sizing:** Generous font sizes and line spacing (line-height of 1.6-1.7) for easy reading. Let the words breathe.  
* **Colour Palette:** Minimal and intentional.  
  * **Primary:** A deep, near-black for text (e.g., \#1D1D1F \- Apple's black) and an off-white or very light grey for the background (e.g., \#F5F5F7). This is easier on the eyes than pure black and white.  
  * **Accent:** One single, calm accent colour inspired by Aotearoa. Think a muted pounamu green (\#2E4D4D), a deep sea blue (\#1A3A5A), or a warm volcanic grey (\#4A4A4A). This colour is used sparingly for links, buttons, and key highlights.  
* **Layout & Spacing (The "GASP"):**  
  * **Whitespace is the main feature.** Use generous padding and margins everywhere. The space around elements is as important as the elements themselves.  
  * **Grid System:** A simple, consistent grid (e.g., a 12-column grid) to ensure alignment and order.  
  * **Asymmetry:** Use asymmetry to create visual interest. For example, an image on one side with text on the other, but not perfectly centred.  
* **Imagery & Video (The "Simple & Effective"):**  
  * **Hero Section:** A subtle, high-quality, slow-motion background video. Not of you, but of a concept.  
    * *Ideas:* Steam gently rising from a long black; ink slowly dispersing in clear water; macro shot of a clean circuit board with light tracing a path; sunlight filtering through native NZ bush. This visually represents "turning chaos into calm."  
  * **Supporting Images:** No corporate stock photos. Use high-resolution, abstract, or nature-inspired images that evoke a feeling of calm and clarity. Think minimalist architecture, serene NZ landscapes, or textured close-ups (wood grain, stone, etc.). Your face should appear, but as a professional, approachable headshot in the "About" section, not as a stock photo model.  
* **Interactivity & Micro-animations:**  
  * **On-Scroll Animations:** Subtle fade-ins and slide-ups as the user scrolls down the page.  
  * **Hover States:** Links and buttons should have a smooth, non-jarring transition on hover (e.g., a gentle colour fade or a slight upward shift).  
  * **Icons:** Use a premium, lightweight icon set (like Feather Icons or Heroicons) for any UI elements.

#### 3\. Site Structure & Pages:

This is a simple sitemap to start. Each page has a clear purpose.

* **/ (Home):** The front door.  
  * **Hero:** A single, powerful statement that hooks the visitor.  
  * **Value Proposition:** Briefly explain the problem you solve ("Calm ops, smart systems").  
  * **Services Overview:** A 3-box summary of your core offerings (e.g., Advisory, Fractional COO, Automation Audits).  
  * **"My Approach or My Principles":** “Smart, Safe, Simple” – one paragraph each.  
  * **Social Proof:** A single, powerful testimonial or a logo of the NFP you're currently helping.  
  * **Final CTA:** A clear, low-friction call to action leading to the contact page eg “Bring clarity to your ops in 90 minutes” – link to booking.  
* **/services (What I Do):**  
  * A dedicated page expanding on your offerings. Frame them as solutions:  
    * **Strategic Advisory:** Helping leadership make sense of the tech landscape.  
    * **Fractional COO/CTO:** Hands-on operational and technical leadership.  
    * **Process & Automation Audit:** A practical audit to find efficiency gains.  
    * **AI Readiness Assessment:** Is AI right for you? A no-nonsense evaluation.  
* **/kōrero (Journal / Substack):**  
  * This page can be a simple, elegant feed of your latest Substack articles. It can either link out directly or use Substack's API to pull in the articles to display on your site, keeping the user within your ecosystem. Naming it "Kōrero" adds a nice local touch.  
* **/AI help**  
  * Services: Discovery session, Ops audit, Automation setup, Team enablement.  
  * Packages: “Start” (fixed‑scope), “Pilot”, “Fractional COO”.  
  * Clear inclusions/exclusions, small table, timeframes, pricing guidance (range).  
  * FAQs: privacy, security, vendor neutrality, training format, data handling.  
  * CTA: booking \+ intake form (choose a problem category).  
* **/lab (Fun/Nerd Stuff):**  
  * This is your space for credibility and personality. A clean grid of cards, each linking to:  
    * A GitHub project with a brief explanation.  
    * A technical write-up.  
    * A cool automation you built.  
    * Keeps the "nerd stuff" separate from the client-facing front page but easily accessible for those who want to see the receipts of your tech chops.  
* **/contact (Let's Kōrero):**  
  * Keep it dead simple.  
  * A warm, inviting message.  
  * Your email address (hyperlinked).  
  * A link to your LinkedIn profile.  
  * **Avoid a contact form.** It adds complexity and a maintenance burden. Directing to email is more personal and fits the "simple, effective" ethos.

#### 4\. Technical stack

* Framework: Astro \+ Islands (Vue components allowed) for minimal JS, perfect with Cloudflare Pages.  
* Styling: Tailwind CSS \+ CSS variables for tokens.  
* Forms: Cloudflare Pages Functions \+ Turnstile, email via Cloudflare Email Routing or Resend.  
* Content: Markdown/MDX for static sections; Substack via RSS fetch at build time or edge cache.  
* Analytics: Plausible (EU‑hosted) or Cloudflare Web Analytics.  
* SEO: Astro SEO integration, canonical tags to Substack for articles, OG images generated at build.  
* Performance: Image component with `<picture>`, font‑display: swap, no blocking webfonts in head.  
* Accessibility: Skip link, visible focus, ARIA on accordions, colour‑contrast tests in CI.

#### Cloudflare services

* Pages (static hosting \+ Functions). • Turnstile (bot‑safe forms). • Images (transform). • Stream (video). • R2 (asset storage) if needed. • D1 (light DB) later for form CRM. • Security headers via Transform Rules.

## **Content kit**

### **Voice principles**

**Plain, direct, curious. No em‑dashes, use en‑dashes. Short sentences. Avoid slang unless it adds warmth.**

### **Copy blocks**

**Hero options**

1. **“Calm ops, smart systems.” Sub: “I help NZ & APAC orgs turn messy processes into safe, simple, intelligent workflows.”**  
2. **“Practical AI for real‑world teams.” Sub: “Security‑first automation and ops – without the drama.”**

**Principles**

* **Smart – measurable impact over theatre.**  
* **Safe – privacy‑aware by design.**  
* **Simple – humans first, fewer moving parts.**

**Service blurbs**

* **Ops Audit – 2 weeks to a no‑nonsense map of risks and wins.**  
* **Pilot – one workflow automated end‑to‑end with training and handover.**  
* **Fractional COO – part‑time leadership to make it stick.**

**CTA microcopy**

* **“Let’s kōrero” • “Book a 30‑min intro” • “Show me the Lab”**

---

## **Integrations**

* **Substack RSS → build‑time cache to JSON → render cards. Canonical to Substack.**  
* **GitHub GraphQL → selected repos \+ stars \+ short description.**  
* **cal.com (or Calendly) → booking widget on AI help \+ Contact.**  
* **Email: `<EMAIL>` via Cloudflare Email Routing.**  
* **Social: LinkedIn primary; TikTok/YouTube as Lab embeds when relevant.**

---

## **Performance budget**

* **HTML \< 20 KB. • CSS \< 15 KB critical. • JS \< 50 KB on Home. • Images lazy‑loaded, hero 1200px, AVIF/WebP.**  
* **No third‑party scripts except analytics \+ booking on dedicated pages.**

Initial Project Setup & Tooling   
Bash

# 1\. Create the Astro project with pnpm

pnpm create astro@latest calmren.com \-- \--template minimal

# 2\. Enter the new directory

cd calmren.com

# 3\. Add your preferred integrations

# Tailwind CSS for styling

pnpm astro add tailwind

# Vue for potential interactive islands (as per your spec)

pnpm astro add vue

# Astro SEO for meta tags and OG images

pnpm astro add astro-seo

# 4\. Install Oxlint for linting

pnpm add \-D oxlint

# 5\. Configure Oxlint by adding a script to your package.json

# "lint": "oxylint . \--fix"

This sequence scaffolds the project exactly as you've outlined.

* Animation Library (GSAP) Your prompt mentions GSAP, but the spec only describes the types of animations (fade-ins, slide-ups). This is a perfect match. You just need to formally add it to the plan.

Action: Install GSAP to handle the on-scroll and hover animations. It's lightweight and powerful, fitting the "premium" feel you're after.

Command: pnpm add gsap

Implementation: You'll likely create a small Astro component or a client-side script in src/scripts/ to initialize GSAP for the subtle scroll-triggered animations.

* Content Placeholders To get started immediately, you can create the initial page structure and content files. This will allow you to build the components around real text from your "Content Kit".

Action: Create the following file structure inside src/pages/ and src/content/.

src/ ├── content/ │   └── testimonials/ │       └── nfp-testimonial.mdx ├── pages/ │   ├── index.astro       (Home page) │   ├── services.astro │   ├── korero.astro      (Journal/Substack) │   ├── ai-help.astro │   ├── lab.astro │   └── contact.astro Populate: Copy and paste the text from your "Content Kit" (Hero options, Principles, Service blurbs) directly into these .astro files as placeholder content.

